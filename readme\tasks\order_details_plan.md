### `/order_details` endpoint
Brief PLAN is to implement the this within @tool_module.py so that this can be used as both:
- As a tool to the agent
- As a direct endpoint available @core.py fastapi

### N8N Node Details
```tenant_id json
{
  "nodes": [
    {
      "parameters": {
        "url": "http://fury-arc-8080a.corp.olaelectric.com/v1/internal/user/2w/details/phone",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "phone_number",
              "value": "={{ $json.body.phone_no }}"
            }
          ]
        },
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "client",
              "value": "arcee"
            },
            {
              "name": "client_token",
              "value": "64148c21-b539-4a22-bbd7-c9577be16ce4"
            },
            {
              "name": "<PERSON><PERSON>",
              "value": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw"
            },
            {
              "name": "cookie",
              "value": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw;"
            }
          ]
        },
        "options": {
          "redirect": {
            "redirect": {}
          }
        }
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1580,
        -140
      ],
      "id": "f9666ce7-6994-4bd7-ac11-f053f58a0501",
      "name": "User Tenant UUID4",
      "onError": "continueErrorOutput"
    }
  ],
  "connections": {
    "User Tenant UUID4": {
      "main": [
        [],
        []
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "87f543696fbbc9747c32cfec9522418972a08ad56edb74442a02423716dbf09d"
  }
}
```

```order_details json
{
  "nodes": [
    {
      "parameters": {
        "url": "=https://arcee-8080b.corp.olaelectric.com/v1/order/{{ $('Order Details Hook').item.json.body.order_id }}/details",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "accept",
              "value": "*/*"
            },
            {
              "name": "X-Purchase-Flow",
              "value": "purchase_flow"
            },
            {
              "name": "locale",
              "value": "en"
            },
            {
              "name": "x-utm-uuid",
              "value": "={{ $json.data.user_tenant_uuid }}"
            }
          ]
        },
        "options": {
          "redirect": {
            "redirect": {}
          }
        }
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1800,
        -240
      ],
      "id": "b5307007-3ccc-459b-bb70-0d4fda95a098",
      "name": "Order Details"
    }
  ],
  "connections": {
    "Order Details": {
      "main": [
        []
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "87f543696fbbc9747c32cfec9522418972a08ad56edb74442a02423716dbf09d"
  }
}
```

### Inputs
Handle edge cases for invalid phone numbers.
```json
{
"phone_no": 
"9322528478",
"order_id": 
"OET-563320315050324-WGQ679"
}
```

### Response Received
```uuid json
[
  {
    "status": "success",
    "data": {
      "user_tenant_uuid": "82c62280-ccb8-400e-8198-192422040213",
      "tenant": "TWO_WHEELER",
      "name": "Technical Shiva",
      "mobile": "9322528478",
      "mobile_verified": true,
      "dialing_code": "+91",
      "email": "<EMAIL>",
      "email_verified": true
    }
  }
]
```

```order_details json
[
  {
    "reserved_date": "2024-03-05T09:33:32.000+00:00",
    "order_metadata": {
      "email_image_url": null,
      "color": "Amethyst",
      "image_url": "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_02.webp",
      "name": "Ola S1 Pro 2nd Gen",
      "range": "181 KM",
      "ip_address": "***********",
      "image_carousal_list": [
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_01.webp",
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_02.webp",
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_03.webp",
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_04.webp",
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_05.webp",
        "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/S1+gen+2+final+Images+/amethyst/S1ProGen02_Amethyst_06.webp"
      ],
      "speed": "116 KM/H",
      "helmet_details": {
        "helmetSize": "LL",
        "productId": "HEL_PO2",
        "helmetPrice": 1299
      },
      "helmet_opted_out": false,
      "insurance_code": "MlwNmyWZdftgKN2Mjao2KctwU273nkNQzRWLGmG7fKI%3D",
      "fame_opted": true,
      "summary_invoked": true,
      "finance_opted": true,
      "journey_id": "C161CC0723B4E5448D4438589BEE146CCC06B127D32FAD473C177BA0AA484C7D",
      "EW_FREE_8YEAR_OFFER": true,
      "invoiceToOfsStatusMap": {
        "down_payment_receipt": {
          "last_try_at": "2024-03-06T17:42:28.251",
          "invoiceId": 7654759,
          "error": "",
          "status": "SUCCESS"
        },
        "advance_payment_receipt": {
          "last_try_at": "2024-03-05T15:14:19.012",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "reservation_payment_receipt": {
          "last_try_at": "2024-03-06T17:42:30.234",
          "invoiceId": 7654761,
          "error": "",
          "status": "SUCCESS"
        },
        "fame_receipt": {
          "last_try_at": "2024-03-06T17:54:59.307",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "split_payment_receipt": {
          "last_try_at": "2024-03-06T17:54:59.315",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "accessory_payment_receipt": {
          "last_try_at": "2024-03-06T17:54:59.322",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "vehicle_invoice": {
          "last_try_at": "2024-03-06T17:54:49.068",
          "invoiceId": 7655860,
          "error": "",
          "status": "SUCCESS"
        },
        "charger_invoice": {
          "last_try_at": "2024-03-06T17:54:49.078",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "registration_invoice": {
          "last_try_at": "2024-03-06T17:54:59.270",
          "invoiceId": 7656011,
          "error": "Exception occured in sending request",
          "status": "FAILURE"
        },
        "EXTENDED_WARRANTY_INVOICE": {
          "last_try_at": "2024-03-06T17:54:59.278",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "EXTENDED_WARRANTY_INVOICE_OEM": {
          "last_try_at": "2024-03-06T17:54:59.286",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "performance_upgrade_invoice": {
          "last_try_at": "2024-03-14T20:07:49.880",
          "invoiceId": 7745843,
          "error": "Error Response from OFS",
          "status": "FAILURE"
        },
        "rto_acknowledgement": {
          "last_try_at": "2024-03-14T20:07:49.891",
          "invoiceId": null,
          "error": "Invoice not found",
          "status": "FAILURE"
        },
        "vehicle_insurance_invoice": {
          "last_try_at": "2024-03-07T17:00:19.992",
          "invoiceId": 0,
          "error": "",
          "status": "SUCCESS"
        },
        "REGISTRATION_CERTIFICATE": {
          "last_try_at": "2024-03-11T21:15:02.836",
          "invoiceId": 0,
          "error": "Error Response from OFS",
          "status": "FAILURE"
        }
      },
      "fame_approved": true,
      "fame_approved_date": "05/03/2024",
      "no_dues": true,
      "sdc": "PUN1",
      "invoice_generated": false,
      "loan_disbursed": true,
      "loan_disbursed_timestamp": 1709810991696,
      "insurance_policy_rollout": true,
      "insurance_policy_rollout_time": "2024-03-07 16:59:56.971",
      "insurance_policy_details": {
        "insurerName": "digit",
        "policyNo": "D138601510",
        "policyDownloadUrl": "https://s3-ap-south-1.amazonaws.com/prod-om/ofs-files-processing/credit-card/insurance/data/policies/vehicle/EV-DIGIT-82c62280-ccb8-400e-8198-192422040213-OET-563320315050324-WGQ679.PDF?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240307T113015Z&X-Amz-SignedHeaders=host&X-Amz-Expires=432000&X-Amz-Credential=AKIA3MMD3GQGSOIQXXIW%2F20240307%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Signature=72950bfa4a050e14beaf2ca5569b311f7aa99337d7ab4309af829b8cbdd22f4e",
        "policyStartDate": "07/03/2024",
        "policyEndDate": "06/03/2029",
        "totalPremium": "10863.0",
        "providedBy": "OFS"
      },
      "c_app_mapping_status": "SUCCESS",
      "uda_id": "65ffced3-bc02-482c-8be2-8b4ec322d504",
      "invoice_status": "Sent to LMD",
      "invoiceStatus": "CANCELED",
      "is_current_owner": true,
      "fancy_opted": false,
      "insurance_generated": true,
      "estimated_delivery_map": {
        "from_date": "2024-03-26",
        "to_date": "2024-03-26"
      },
      "original_estimated_delivery_map": {
        "original_end_date": "2024-03-24",
        "original_from_date": "2024-03-24"
      },
      "is_user_dependency_applicable": false,
      "user_dependency_days": 2,
      "order_dependencies": [
        {
          "dependency": "DOCUMENTS_UPLOAD_PENDING",
          "dependency_status": "COMPLETED",
          "dependency_start_date": "2024-03-05T09:44:13.000+00:00",
          "dependency_end_date": "2024-03-05T10:15:37.000+00:00",
          "dependency_type": "CUSTOMER",
          "metadata": {}
        },
        {
          "dependency": "LOAN_JOURNEY_PENDING",
          "dependency_status": "COMPLETED",
          "dependency_start_date": "2024-03-05T09:42:29.000+00:00",
          "dependency_end_date": "2024-03-06T12:12:26.000+00:00",
          "dependency_type": "CUSTOMER",
          "metadata": {}
        },
        {
          "dependency": "DOCUMENTS_REJECTED",
          "dependency_status": "COMPLETED",
          "dependency_start_date": "2024-03-05T11:56:39.000+00:00",
          "dependency_end_date": "2024-03-06T11:34:21.000+00:00",
          "dependency_type": "CUSTOMER",
          "metadata": {}
        },
        {
          "dependency": "LOAN_DISBURSAL_PENDING",
          "dependency_status": "COMPLETED",
          "dependency_start_date": "2024-03-06T12:24:46.000+00:00",
          "dependency_end_date": "2024-03-07T11:29:52.000+00:00",
          "dependency_type": "BUSINESS",
          "metadata": {}
        }
      ],
      "balance_payment_whitelisted": true,
      "balance_payment_date": 1709631749000,
      "insurance_service_failed": false
    },
    "delivery_date": "2024-03-14",
    "title": "Congrats! Your scooter is %%delivered!%%",
    "event_list": [
      "view_receipts",
      "get_support",
      "reserve_another",
      "view_insurance_policy",
      "view_extended_warranty_policy",
      "view_ola_care_policy"
    ],
    "payment_details_dto_list": [
      {
        "order_id": "OET-563320315050324-WGQ679",
        "user_id": null,
        "payment_type": "PRE_ORDER_PAYMENT",
        "status": "SUCCESS",
        "amount": 0,
        "transaction_id": "tx-734320315050324-JR37",
        "metadata": {},
        "created": 1709631212000,
        "approval_status": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "user_id": null,
        "payment_type": "PURCHASE_PAYMENT",
        "status": "SUCCESS",
        "amount": 0,
        "transaction_id": "tx-738320315050324-GR18",
        "metadata": {},
        "created": 1709631212000,
        "approval_status": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "user_id": null,
        "payment_type": "DOWN_PAYMENT",
        "status": "SUCCESS",
        "amount": 18690,
        "transaction_id": "tx-463291215050324-JN72",
        "metadata": {},
        "created": 1709631749000,
        "approval_status": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "user_id": null,
        "payment_type": "OLA_CARE_PLUS_PAYMENT",
        "status": "SUCCESS",
        "amount": 4129,
        "transaction_id": "tx-965493411110824-KB17",
        "metadata": {
          "source": "CAPP"
        },
        "created": 1723356290000,
        "approval_status": null
      }
    ],
    "allowed_products_response": [
      {
        "product_id": "PRO_001_EN_IN",
        "product_name": "Ola S1"
      },
      {
        "product_id": "PRO_002_EN_IN",
        "product_name": "Ola S1 Pro"
      },
      {
        "product_id": "PRO_003_EN_IN",
        "product_name": "Ola S1 Air"
      },
      {
        "product_id": "PRO_004_EN_IN",
        "product_name": "Ola S1 Pro 2nd Gen"
      },
      {
        "product_id": "PRO_005_EN_IN",
        "product_name": "Ola S1 X Plus"
      },
      {
        "product_id": "PRO_005_02_EN_IN",
        "product_name": "Ola S1 X 2kWh"
      },
      {
        "product_id": "PRO_005_03_EN_IN",
        "product_name": "Ola S1 X 3kWh"
      },
      {
        "product_id": "PRO_005_04_EN_IN",
        "product_name": "Ola S1 X 4kWh"
      },
      {
        "product_id": "PRO_005_05_EN_IN",
        "product_name": "Ola S1 X Plus 4kWh"
      },
      {
        "product_id": "PRO_011_01_EN_IN",
        "product_name": "Roadster X 2.5kWh"
      },
      {
        "product_id": "PRO_011_02_EN_IN",
        "product_name": "Roadster X 3.5kWh"
      },
      {
        "product_id": "PRO_011_03_EN_IN",
        "product_name": "Roadster X 4.5kWh"
      },
      {
        "product_id": "PRO_005_06_EN_IN",
        "product_name": "Ola S1 X Plus 3rd Gen"
      },
      {
        "product_id": "PRO_005_07_EN_IN",
        "product_name": "Ola S1 X 3rd Gen 2kWh"
      },
      {
        "product_id": "PRO_005_08_EN_IN",
        "product_name": "Ola S1 X 3rd Gen 3kWh"
      },
      {
        "product_id": "PRO_005_09_EN_IN",
        "product_name": "Ola S1 X 3rd Gen 4kWh"
      },
      {
        "product_id": "PRO_006_EN_IN",
        "product_name": "Ola S1 Pro Plus 3rd Gen 4kWh"
      },
      {
        "product_id": "PRO_006_01_EN_IN",
        "product_name": "Ola S1 Pro 3rd Gen 3kWh"
      },
      {
        "product_id": "PRO_006_02_EN_IN",
        "product_name": "Ola S1 Pro 3rd Gen 4kWh"
      },
      {
        "product_id": "PRO_006_03_EN_IN",
        "product_name": "Ola S1 Pro Plus 3rd Gen 5.3kWh"
      },
      {
        "product_id": "PRO_009_EN_IN",
        "product_name": "Roadster X Plus 4.5 kWh"
      },
      {
        "product_id": "PRO_009_01_EN_IN",
        "product_name": "Roadster X Plus 9.1 kWh"
      }
    ],
    "show_extended_warranty_card": false,
    "free_extended_warranty_offer_enabled": true,
    "not_bought_batt_comp": false,
    "show_ola_care_card": true,
    "show_extended_warranty_upgrade_card": false,
    "feature_pack_order_details": {
      "is_free_trial": false,
      "show_feature_pack_card": true,
      "is_feature_pack_purchased": false,
      "expiry_in": 0
    },
    "delivery_location_details": {
      "payment_required": true,
      "change_request_by_lmd": false,
      "home_delivery_payment_amount": 999,
      "current_delivery_type": "STORE_PICKUP",
      "experience_centre_id": "XC-Pune-Erandwane",
      "scooter_delivery_pincode": "411058"
    },
    "document_rejected": false,
    "rejected_list": [],
    "offline_payment_pending": false,
    "order_details_dto_list": [
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "EXTENDED_WARRANTY",
        "product_id": "EW_S1PRO_GEN2_8YR_80K",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": null,
        "image_url": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "OLA_CARE",
        "product_id": "OLA_CARE_PLUS_S1_PRO_GEN2",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": null,
        "image_url": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "VEHICLE",
        "product_id": "PRO_004_EN_IN",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": "SCOOTER",
        "image_url": "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/scooter/scooter.jpg"
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "OPTION",
        "product_id": "234",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": null,
        "image_url": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "HELMET",
        "product_id": "HEL_PO2",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 1299,
        "sub_type": null,
        "image_url": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "HELMET_OPTION",
        "product_id": "186",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": null,
        "image_url": null
      },
      {
        "order_id": "OET-563320315050324-WGQ679",
        "product_type": "DELIVERY",
        "product_id": "STORE_PICKUP",
        "quantity": 1,
        "status": "DELIVERED",
        "amount": 0,
        "sub_type": null,
        "image_url": null
      }
    ],
    "order_details_next_steps": [
      {
        "key": "down_payment",
        "title": "Down payment paid",
        "sequence_id": 1,
        "status": "completed"
      },
      {
        "key": "auto_debit",
        "title": "Auto-debit & loan document",
        "sequence_id": 2,
        "status": "completed",
        "data": {
          "Loan amount": "131000",
          "Loan document": "https://asset-financing-marketplace-prod.s3.amazonaws.com/user_provider_doc/C161CC0723B4E5448D4438589BEE146CCC06B127D32FAD473C177BA0AA484C7D-ola_electric/liquiloan/agreement",
          "Loan ID": "3044367.0",
          "Duration": "24months",
          "Bank name": "LiquiLoans",
          "EMI amount": "₹6,546/month"
        }
      },
      {
        "key": "upload_documents",
        "title": "Document verification done",
        "sequence_id": 3,
        "status": "completed"
      },
      {
        "key": "short_collection",
        "title": "Short Collection Pending",
        "sequence_id": 4,
        "status": "not_started"
      },
      {
        "key": "scooter_ready",
        "title": "Scooter dispatched",
        "sequence_id": 5,
        "status": "completed"
      },
      {
        "key": "scooter_registration",
        "title": "Scooter registration done",
        "sequence_id": 6,
        "status": "completed"
      },
      {
        "key": "out_for_delivery",
        "title": "Scooter delivered",
        "text": "Congrats! Enjoy your new ride",
        "sequence_id": 7,
        "status": "completed"
      }
    ],
    "special_order": "Normal",
    "delivery_appointment_status": "NOT ELIGIBLE",
    "available_invoice_list": [
      {
        "document_id": "35927052-efc7-41d8-9d5a-7f3229c03196",
        "document_type": "ola_care_invoice"
      },
      {
        "document_id": "6aeb970a-cfaf-4fbd-a20b-0aa2fb1ee836",
        "document_type": "performance_upgrade_invoice"
      },
      {
        "document_id": "3e8223cf-4852-46b1-b3fa-f33e2554d79e",
        "document_type": "vehicle_insurance_invoice"
      },
      {
        "document_id": "4dd9083f-1794-4e1b-9813-9820b26dc333",
        "document_type": "vehicle_invoice"
      }
    ]
  }
]
```

