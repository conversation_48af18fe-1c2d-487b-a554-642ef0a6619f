// BIG FAT WARNING: to avoid the complexity of npm, this typescript is compiled in the browser
// there's currently no static type checking

import { marked } from 'https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.0/lib/marked.esm.js'

let quickStatResponseData: string | null = null;
let orderListResponseData: string | null = null;

const convElement = document.getElementById('conversation')

const promptInput = document.getElementById('prompt-input') as HTMLInputElement | null
const spinner = document.getElementById('spinner')
const modelSelect = document.getElementById('model-select') as HTMLSelectElement | null
const temperatureSlider = document.getElementById('temperature-slider') as HTMLInputElement | null
const temperatureValue = document.getElementById('temperature-value') as HTMLSpanElement | null
const userIdInput = document.getElementById('user-id-input') as HTMLInputElement | null;
const sessionIdInput = document.getElementById('session-id-input') as HTMLInputElement | null;
const vinInput = document.getElementById('vin-input') as HTMLInputElement | null;
const phoneInput = document.getElementById('phone-input') as HTMLInputElement | null;
const orderIdInput = document.getElementById('order-id-input') as HTMLInputElement | null;
const vinStatus = document.getElementById('vin-status') as HTMLSpanElement | null;
const phoneStatus = document.getElementById('phone-status') as HTMLSpanElement | null;
const orderIdStatus = document.getElementById('order-id-status') as HTMLSpanElement | null;
const regenerateUserIdBtn = document.getElementById('regenerate-user-id') as HTMLButtonElement | null;
const regenerateSessionIdBtn = document.getElementById('regenerate-session-id') as HTMLButtonElement | null;
const memoryToggle = document.getElementById('memory-toggle') as HTMLInputElement | null;

// stream the response and render messages as each chunk is received
// data is sent as newline-delimited JSON
async function onFetchResponse(response: Response): Promise<void> {
  let text = ''
  let decoder = new TextDecoder()
  if (response.ok) {
    if (!response.body) return;
    const reader = response.body.getReader()
    while (true) {
      const {done, value} = await reader.read()
      if (done) {
        break
      }
      text += decoder.decode(value)
      addMessages(text)
      if (spinner) spinner.classList.remove('active')
    }
    addMessages(text)
    if (promptInput) {
      promptInput.disabled = false
      promptInput.focus()
    }
  } else {
    const text = await response.text()
    console.error(`Unexpected response: ${response.status}`, {response, text})
    throw new Error(`Unexpected response: ${response.status}`)
  }
}

// The format of messages, this matches pydantic-ai both for brevity and understanding
// in production, you might not want to keep this format all the way to the frontend
interface Message {
  role: string
  content: string
  timestamp: string
}

// take raw response text and render messages into the `#conversation` element
// Message timestamp is assumed to be a unique identifier of a message, and is used to deduplicate
// hence you can send data about the same message multiple times, and it will be updated
// instead of creating a new message elements
function addMessages(responseText: string) {
  if (!convElement) return;
  const lines = responseText.split('\n')
  const messages: Message[] = lines.filter(line => line.length > 1).map(j => JSON.parse(j))
  for (const message of messages) {
    // we use the timestamp as a crude element id
    const {timestamp, role, content} = message
    const id = `msg-${timestamp}`
    let msgDiv = document.getElementById(id)
    if (!msgDiv) {
      msgDiv = document.createElement('div')
      msgDiv.id = id
      msgDiv.title = `${role} at ${timestamp}`
      msgDiv.classList.add('border-top', 'pt-2', role)
      convElement.appendChild(msgDiv)
    }
    msgDiv.innerHTML = marked.parse(content)
  }
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
}

function onError(error: any) {
  console.error(error)
  const errorDiv = document.getElementById('error')
  if (errorDiv) errorDiv.classList.remove('d-none')
  const spinnerDiv = document.getElementById('spinner')
  if (spinnerDiv) spinnerDiv.classList.remove('active')
}

function randomId(prefix: string) {
  return prefix + '-' + Math.random().toString(36).slice(2, 10);
}

function setRandomUserId() {
  if (userIdInput) userIdInput.value = randomId('user');
}
function setRandomSessionId() {
  if (sessionIdInput) sessionIdInput.value = randomId('sess');
}

function clearConversation() {
  if (convElement) {
    convElement.innerHTML = '';
  }
}

function setRandomUserIdAndClear() {
  setRandomUserId();
  clearConversation();
}
function setRandomSessionIdAndClear() {
  setRandomSessionId();
  clearConversation();
}

if (regenerateUserIdBtn) regenerateUserIdBtn.addEventListener('click', setRandomUserIdAndClear);
if (regenerateSessionIdBtn) regenerateSessionIdBtn.addEventListener('click', setRandomSessionIdAndClear);
// Set initial values on page load
setRandomUserId();
setRandomSessionId();

function fetchSessionChatHistory(sessionId: string) {
  fetch(`http://localhost:8000/chat/${encodeURIComponent(sessionId)}/`)
    .then(onFetchResponse)
    .catch(onError);
}

if (sessionIdInput) {
  sessionIdInput.addEventListener('change', () => {
    if (sessionIdInput.value) {
      fetchSessionChatHistory(sessionIdInput.value);
    }
  });
}

// On page load, fetch chat history for the initial session
if (sessionIdInput && sessionIdInput.value) {
  fetchSessionChatHistory(sessionIdInput.value);
}

async function onSubmit(e: SubmitEvent): Promise<void> {
  e.preventDefault()
  if (!spinner || !promptInput || !modelSelect || !temperatureSlider || !userIdInput || !sessionIdInput || !memoryToggle) return;
  spinner.classList.add('active')
  const prompt = promptInput.value
  const model = modelSelect.value
  const temperature = parseFloat(temperatureSlider.value)
  const user_id = userIdInput.value
  const session_id = sessionIdInput.value
  const enable_memory = memoryToggle.checked;
  const vin = vinInput ? vinInput.value : '';
  const phone = phoneInput ? phoneInput.value : '';
  const order_id = orderIdInput ? orderIdInput.value : '';

  const body = JSON.stringify({
    user_id: user_id,
    session_id: session_id,
    prompt: prompt,
    model: model,
    temperature: temperature,
    enable_memory: enable_memory,
    vin: vin,
    phone: phone,
    order_id: order_id
  })

  promptInput.value = ''
  promptInput.disabled = true

  const response = await fetch(`http://localhost:8000/chat/${encodeURIComponent(session_id)}/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body
  })
  await onFetchResponse(response)
}

// call onSubmit when the form is submitted (e.g. user clicks the send button or hits Enter)
document.querySelector('form')?.addEventListener('submit', (e) => onSubmit(e).catch(onError))

if (temperatureSlider && temperatureValue) {
  temperatureSlider.addEventListener('input', () => {
    temperatureValue.textContent = parseFloat(temperatureSlider.value).toFixed(2);
  });
}

// Basic validation functions for the input fields
function validateVIN(value: string): { isValid: boolean; message: string } {
  if (!value) {
    return { isValid: false, message: 'Not Validated' };
  }
  // Basic VIN validation (17 characters, alphanumeric except I, O, Q)
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/;
  return vinRegex.test(value)
    ? { isValid: true, message: 'Valid VIN' }
    : { isValid: false, message: 'Invalid VIN' };
}

function validatePhone(value: string): { isValid: boolean; message: string } {
  if (!value) {
    return { isValid: false, message: 'Not Validated' };
  }
  // Basic phone validation (10-15 digits, optionally with formatting)
  const digitsOnly = value.replace(/\D/g, '');
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(digitsOnly)
    ? { isValid: true, message: 'Valid Phone' }
    : { isValid: false, message: 'Invalid Phone' };
}

function validateOrderId(value: string): { isValid: boolean; message: string } {
  if (!value) {
    return { isValid: false, message: 'Not Validated' };
  }
  // Basic order ID validation (alphanumeric, at least 5 characters)
  const orderIdRegex = /^[A-Za-z0-9-]{5,}$/;
  return orderIdRegex.test(value)
    ? { isValid: true, message: 'Valid Order ID' }
    : { isValid: false, message: 'Invalid Order ID' };
}

// Update status badge based on validation result
function updateStatusBadge(badge: HTMLElement | null, isValid: boolean, message: string) {
  if (!badge) return;

  badge.textContent = message;
  badge.className = 'badge';

  if (isValid) {
    badge.classList.add('bg-success');
  } else if (message === 'Not Validated') {
    badge.classList.add('bg-secondary');
  } else {
    badge.classList.add('bg-danger');
  }
}

// Add event listeners for validation
if (vinInput && vinStatus) {
  vinInput.addEventListener('blur', async () => {
    // const result = validateVIN(vinInput.value); // Keep or remove local validation as needed
    // updateStatusBadge(vinStatus, result.isValid, result.message); // Keep or remove local validation as needed
    if (vinInput.value) {
      try {
        const response = await fetch('/quick_stat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ vin_number: vinInput.value }),
        });
        if (response.ok) {
          quickStatResponseData = await response.text();
          if (vinStatus) {
            vinStatus.textContent = 'Validated';
            vinStatus.className = 'badge bg-success';
          }
        } else {
          quickStatResponseData = null;
          if (vinStatus) {
            vinStatus.textContent = 'Error';
            vinStatus.className = 'badge bg-danger';
          }
          console.error('Error fetching quick_stat:', response.status, await response.text());
        }
      } catch (error) {
        quickStatResponseData = null;
        if (vinStatus) {
          vinStatus.textContent = 'Error';
          vinStatus.className = 'badge bg-danger';
        }
        console.error('Error fetching quick_stat:', error);
      }
    } else {
      quickStatResponseData = null;
      if (vinStatus) {
        vinStatus.textContent = 'Not Validated';
        vinStatus.className = 'badge bg-secondary';
      }
    }
  });
}

if (phoneInput && phoneStatus) {
  phoneInput.addEventListener('blur', async () => {
    // const result = validatePhone(phoneInput.value); // Keep or remove local validation as needed
    // updateStatusBadge(phoneStatus, result.isValid, result.message); // Keep or remove local validation as needed
    if (phoneInput.value) {
      try {
        const response = await fetch('/order_list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ phone_number: phoneInput.value }),
        });
        if (response.ok) {
          orderListResponseData = await response.text();
          if (phoneStatus) {
            phoneStatus.textContent = 'Validated';
            phoneStatus.className = 'badge bg-success';
          }
        } else {
          orderListResponseData = null;
          if (phoneStatus) {
            phoneStatus.textContent = 'Error';
            phoneStatus.className = 'badge bg-danger';
          }
          console.error('Error fetching order_list:', response.status, await response.text());
        }
      } catch (error) {
        orderListResponseData = null;
        if (phoneStatus) {
          phoneStatus.textContent = 'Error';
          phoneStatus.className = 'badge bg-danger';
        }
        console.error('Error fetching order_list:', error);
      }
    } else {
      orderListResponseData = null;
      if (phoneStatus) {
        phoneStatus.textContent = 'Not Validated';
        phoneStatus.className = 'badge bg-secondary';
      }
    }
  });
}

if (orderIdInput && orderIdStatus) {
  orderIdInput.addEventListener('blur', () => {
    const result = validateOrderId(orderIdInput.value);
    updateStatusBadge(orderIdStatus, result.isValid, result.message);
  });
}