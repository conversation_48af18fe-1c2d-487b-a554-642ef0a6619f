from __future__ import annotations as _annotations

import json
import os
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict

import fastapi
from fastapi import Depends, Request, UploadFile, File, Form
from fastapi.responses import FileResponse, Response, StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from pydantic_ai.exceptions import UnexpectedModelBehavior, ModelHTTPError
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    TextPart,
    UserPromptPart,
    SystemPromptPart
)

from src.models import ChatMessage, RequestModel, QuickStatRequest, OrderListRequest, OrderDetailsRequest
from src.agent import AgentModule
from src.database import SessionDatabase, Database
from src.integrations.tool_module import ToolManager
from src.integrations.mcp_module import MCPManager
from src.prompts.loader import get_system_prompt_with_chat_memory
from src.integrations.mem0_module import Mem0Manager
from src.integrations.langfuse import LangfuseIntegration
from loguru import logger
from opentelemetry.trace import Span<PERSON>ind
from groq import Groq

from dotenv import load_dotenv

load_dotenv()

# Logging setup as per project rules
LOG_DIR = os.path.join(os.path.dirname(__file__), '../logs/core')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

THIS_DIR = Path(__file__).parent
CWD = Path.cwd()
CONFIG_DIR = THIS_DIR / 'config'
INTEGRATIONS_FILE = CONFIG_DIR / 'integrations.json'


class IntegrationsManager:
    """
    Manages the health and status of integrations (tools and MCP servers).
    Performs health checks and stores results in integrations.json.
    """
    def __init__(self):
        self.tool_manager = ToolManager()
        self.mcp_manager = MCPManager()
        # Prepare config for LangfuseIntegration
        self.langfuse_config = {
            "LANGFUSE_DEBUG": os.getenv("LANGFUSE_DEBUG", "0") == "1",
            "LANGFUSE_public_key": os.getenv("LANGFUSE_public_key"),
            "LANGFUSE_secret_key": os.getenv("LANGFUSE_secret_key"),
        }
        self.langfuse = LangfuseIntegration(self.langfuse_config)
        os.makedirs(CONFIG_DIR, exist_ok=True)
        
    async def check_all_integrations(self):
        """Perform health checks on all integrations and update status file."""
        # Check tool health
        tool_health, tool_error = self.tool_manager.check_health()
        
        # Check MCP servers health
        mcp_servers = await self.mcp_manager.get_mcp_servers()
        mcp_server_statuses = []
        for server in mcp_servers:
            mcp_server_statuses.append({
                "url": server.url,
                "status": True
            })
        # Langfuse health
        langfuse_health, langfuse_error = await self.langfuse.check_health()
        
        # Update integrations.json
        integrations_data = {
            "last_updated": datetime.now(tz=timezone.utc).isoformat(),
            "tools": {
                "status": tool_health,
                "error": tool_error
            },
            "mcp_servers": mcp_server_statuses,
            "langfuse": {
                "status": langfuse_health,
                "error": langfuse_error
            }
        }
        
        # Write to file
        with open(INTEGRATIONS_FILE, 'w') as f:
            json.dump(integrations_data, f, indent=2)
            
        return integrations_data
    
    @staticmethod
    def get_integration_status() -> Dict:
        """Read the current integration status from file."""
        if not INTEGRATIONS_FILE.exists():
            return {
                "last_updated": None,
                "tools": {"status": False, "error": "Integrations file not found"},
                "mcp_servers": [],
                "langfuse": {"status": False, "error": "Integrations file not found"}
            }
            
        try:
            with open(INTEGRATIONS_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            return {
                "last_updated": None,
                "tools": {"status": False, "error": f"Error reading integrations file: {str(e)}"},
                "mcp_servers": [],
                "langfuse": {"status": False, "error": f"Error reading integrations file: {str(e)}"}
            }


@asynccontextmanager
async def lifespan(_app: fastapi.FastAPI):
    # Initialize database
    async with Database.connect() as db:
        # Run integration health checks at startup
        integration_manager = IntegrationsManager()
        await integration_manager.check_all_integrations()
        
        yield {'db': db, 'integration_manager': integration_manager}


app = fastapi.FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # restrict to your frontend origin
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get('/')
async def index() -> FileResponse:
    return FileResponse((CWD / 'www/chat_app.html'), media_type='text/html')


@app.get('/chat_app.ts')
async def main_ts() -> FileResponse:
    """Get the raw typescript code, it's compiled in the browser, forgive me."""
    return FileResponse((CWD / 'www/chat_app.ts'), media_type='text/plain')


async def get_db(request: Request) -> Database:
    return request.state.db


async def get_integration_manager(request: Request) -> IntegrationsManager:
    return request.state.integration_manager


@app.get('/chat/')
async def get_chat(database: Database = Depends(get_db)) -> Response:
    msgs = await database.get_messages()
    return Response(
        b'\n'.join(json.dumps(to_chat_message(m)).encode('utf-8') for m in msgs),
        media_type='text/plain',
    )


@app.get('/chat/{session_id}/')
async def get_chat_session(session_id: str) -> Response:
    async with SessionDatabase.connect() as session_db:
        msgs = await session_db.get_messages(session_id)
    return Response(
        b'\n'.join(json.dumps(to_chat_message(m)).encode('utf-8') for m in msgs),
        media_type='text/plain',
    )


def to_chat_message(m: ModelMessage) -> ChatMessage:
    first_part = m.parts[0]
    if isinstance(m, ModelRequest):
        if isinstance(first_part, UserPromptPart):
            assert isinstance(first_part.content, str)
            return {
                'role': 'user',
                'timestamp': first_part.timestamp.isoformat(),
                'content': first_part.content,
            }
    elif isinstance(m, ModelResponse):
        if isinstance(first_part, TextPart):
            return {
                'role': 'model',
                'timestamp': m.timestamp.isoformat(),
                'content': first_part.content,
            }
    raise UnexpectedModelBehavior(f'Unexpected message type for chat app: {m}')


# Health check endpoint
@app.get('/health')
async def health():
    return {"status": "ok"}


# Integration status endpoints
@app.get('/integrations/status')
async def get_integrations_status():
    """Get the current status of all integrations."""
    return IntegrationsManager.get_integration_status()


@app.post('/integrations/refresh')
async def refresh_integrations(
    integration_manager: IntegrationsManager = Depends(get_integration_manager)
):
    """Refresh the health check status of all integrations."""
    return await integration_manager.check_all_integrations()


@app.post('/chat/')
async def post_chat(
    request: RequestModel, database: Database = Depends(get_db)
) -> StreamingResponse:
    # Select agent based on the model field and temperature
    agent_module = AgentModule(request.model, model_settings={"temperature": request.temperature})
    agent = agent_module.get_agent()
    
    async def stream_messages():
        yield (
            json.dumps(
                {
                    'role': 'user',
                    'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                    'content': request.prompt,
                }
            ).encode('utf-8')
            + b'\n'
        )
        messages = await database.get_messages()
        
        # Use MCP servers within a proper context manager
        async with agent.run_mcp_servers():
            async with agent.run_stream(request.prompt, message_history=messages) as result:
                async for text in result.stream(debounce_by=0.01):
                    m = ModelResponse(parts=[TextPart(text)], timestamp=result.timestamp())
                    yield json.dumps(to_chat_message(m)).encode('utf-8') + b'\n'
            await database.add_messages(result.new_messages_json())
            
    return StreamingResponse(stream_messages(), media_type='text/plain')


@app.post('/chat/{session_id}/')
async def post_chat_session(
    session_id: str, request: RequestModel
) -> StreamingResponse:
    agent_module = AgentModule(request.model, model_settings={"temperature": request.temperature})
    agent = agent_module.get_agent()
    user_id = getattr(request, 'user_id', None)
    user_query = getattr(request, 'prompt', None)
    enable_memory = getattr(request, 'enable_memory', True)

    # Build system prompt with or without user memory context
    if enable_memory and user_id and user_query:
        system_prompt = get_system_prompt_with_chat_memory(user_id, user_query)
    else:
        from src.prompts.loader import get_system_prompt
        system_prompt = get_system_prompt() if user_id and user_query else None

    # Langfuse integration setup
    integration_status = agent_module._get_integration_status()
    langfuse_status = integration_status.get('langfuse', {}).get('status', False)
    langfuse_tracer = None
    if langfuse_status:
        config = {
            "LANGFUSE_DEBUG": os.getenv("LANGFUSE_DEBUG", "0") == "1",
            "LANGFUSE_public_key": os.getenv("LANGFUSE_public_key"),
            "LANGFUSE_secret_key": os.getenv("LANGFUSE_secret_key"),
        }
        langfuse = LangfuseIntegration(config)
        langfuse_tracer = langfuse.tracer

    async def stream_messages():
        # Yield the user message first
        yield (
            json.dumps(
                {
                    'role': 'user',
                    'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                    'content': user_query,
                }
            ).encode('utf-8')
            + b'\n'
        )
        async with SessionDatabase.connect() as session_db:
            messages = await session_db.get_messages(session_id)

            # Insert system prompt as the first message if available
            if system_prompt:
                messages = [ModelRequest(parts=[SystemPromptPart(content=system_prompt, timestamp=datetime.now(tz=timezone.utc))])] + messages
            logger.info(f"Messages: {messages}")

            # Langfuse tracing block
            if langfuse_tracer is not None:
                with langfuse_tracer.start_as_current_span("chat_session", kind=SpanKind.INTERNAL) as span:
                    span.set_attribute("langfuse.user.id", user_id or "unknown")
                    span.set_attribute("langfuse.session.id", session_id)
                    span.set_attribute("langfuse.tags", ["chat", "mcp"])
                    span.set_attribute("input.prompt", user_query)
                    span.set_attribute("input.model", request.model)
                    span.set_attribute("input", user_query)  # For Langfuse UI main Input field
                    
                    logger.debug(f"Agent System Prompt (Langfuse Path): {system_prompt}")
                    logger.debug(f"Agent User Query (Langfuse Path): {user_query}")
                    logger.debug(f"Agent Message History (Langfuse Path): {messages}")
                    try:
                        logger.debug(f"Agent Tools (Langfuse Path): {agent.tools}")
                        logger.debug(f"Agent MCP Servers (Langfuse Path): {agent.mcp_servers}")
                    except Exception as log_ex:
                        logger.warning(f"Could not log agent tools/mcp_servers: {log_ex}")

                    try:
                        # Use MCP servers within a proper context manager
                        async with agent.run_mcp_servers():
                            async with agent.run_stream(user_query, message_history=messages) as result:
                                llm_response_text = ""
                                async for text in result.stream(debounce_by=0.01):
                                    llm_response_text += text
                                    m = ModelResponse(parts=[TextPart(text)], timestamp=result.timestamp())
                                    yield json.dumps(to_chat_message(m)).encode('utf-8') + b'\n'
                                # Store the new interaction in memory after LLM response if enabled
                                if enable_memory:
                                    try:
                                        mem_manager = Mem0Manager()
                                        mem_manager.add(user_id, [
                                            {"role": "user", "content": user_query},
                                            {"role": "assistant", "content": llm_response_text}
                                        ])
                                        logger.info(f"Stored memory for user_id={user_id}")
                                    except Exception as e_mem:
                                        logger.error(f"Error storing memory for user_id={user_id}: {e_mem}")
                                await session_db.add_message(session_id, result.new_messages_json(), datetime.now(tz=timezone.utc).isoformat())
                                # Set output attribute after agent call
                                span.set_attribute("output.llm_response", llm_response_text)
                                span.set_attribute("output", llm_response_text)  # For Langfuse UI main Output field
                    except ModelHTTPError as e_mhttp:
                        logger.error(f"ModelHTTPError during agent.run_stream (Langfuse Path): {e_mhttp}")
                        logger.error(f"ModelHTTPError Status Code: {e_mhttp.status_code}")
                        logger.error(f"ModelHTTPError Model Name: {e_mhttp.model_name}")
                        logger.error(f"ModelHTTPError Body: {e_mhttp.body}")
                        raise
                    except Groq.APIError as e_groq_api: # Catching Groq.APIError specifically if it's still relevant
                        logger.error(f"Groq.APIError during agent.run_stream (Langfuse Path): {e_groq_api}")
                        if hasattr(e_groq_api, 'body'):
                            logger.error(f"Groq.APIError body: {e_groq_api.body}")
                        if hasattr(e_groq_api, 'json_body'):
                            logger.error(f"Groq.APIError json_body: {e_groq_api.json_body}")
                        logger.error(f"Groq.APIError attributes: {dir(e_groq_api)}")
                        raise
                    except Exception as e_gen:
                        logger.error(f"Generic Exception during agent.run_stream (Langfuse Path): {e_gen}", exc_info=True)
                        raise
            else:
                # No Langfuse tracing
                logger.debug(f"Agent System Prompt (No Langfuse Path): {system_prompt}")
                logger.debug(f"Agent User Query (No Langfuse Path): {user_query}")
                logger.debug(f"Agent Message History (No Langfuse Path): {messages}")
                try:
                    logger.debug(f"Agent Tools (No Langfuse Path): {agent.tools}")
                    logger.debug(f"Agent MCP Servers (No Langfuse Path): {agent.mcp_servers}")
                except Exception as log_ex:
                    logger.warning(f"Could not log agent tools/mcp_servers: {log_ex}")

                try:
                    async with agent.run_mcp_servers():
                        async with agent.run_stream(user_query, message_history=messages) as result:
                            llm_response_text = ""
                            async for text in result.stream(debounce_by=0.01):
                                llm_response_text += text
                                m = ModelResponse(parts=[TextPart(text)], timestamp=result.timestamp())
                                yield json.dumps(to_chat_message(m)).encode('utf-8') + b'\n'
                            # Store the new interaction in memory after LLM response if enabled
                            if enable_memory:
                                try:
                                    mem_manager = Mem0Manager()
                                    mem_manager.add(user_id, [
                                        {"role": "user", "content": user_query},
                                        {"role": "assistant", "content": llm_response_text}
                                    ])
                                    logger.info(f"Stored memory for user_id={user_id}")
                                except Exception as e_mem:
                                    logger.error(f"Error storing memory for user_id={user_id}: {e_mem}")
                            await session_db.add_message(session_id, result.new_messages_json(), datetime.now(tz=timezone.utc).isoformat())
                except ModelHTTPError as e_mhttp:
                    logger.error(f"ModelHTTPError during agent.run_stream (No Langfuse Path): {e_mhttp}")
                    logger.error(f"ModelHTTPError Status Code: {e_mhttp.status_code}")
                    logger.error(f"ModelHTTPError Model Name: {e_mhttp.model_name}")
                    logger.error(f"ModelHTTPError Body: {e_mhttp.body}")
                    raise
                except Groq.APIError as e_groq_api: # Catching Groq.APIError specifically
                    logger.error(f"Groq.APIError during agent.run_stream (No Langfuse Path): {e_groq_api}")
                    if hasattr(e_groq_api, 'body'):
                        logger.error(f"Groq.APIError body: {e_groq_api.body}")
                    if hasattr(e_groq_api, 'json_body'):
                        logger.error(f"Groq.APIError json_body: {e_groq_api.json_body}")
                    logger.error(f"Groq.APIError attributes: {dir(e_groq_api)}")
                    raise
                except Exception as e_gen:
                    logger.error(f"Generic Exception during agent.run_stream (No Langfuse Path): {e_gen}", exc_info=True)
                    raise

    return StreamingResponse(stream_messages(), media_type='text/plain')

@app.post('/quick_stat')
async def quick_stat(request: QuickStatRequest):
    logger.info(f"Request: {request}")
    tm = ToolManager()
    result = tm.get_vehicle_quick_stat(request.vin_number)
    return result


@app.post('/order_details')
async def order_details(request: OrderDetailsRequest):
    logger.info(f"OrderDetailsRequest: {request}")
    tm = ToolManager()
    result = await tm.get_order_details(request.phone_no, request.order_id)
    return result

# Order list endpoint
@app.post('/order_list')
async def order_list(request: OrderListRequest):
    logger.info(f"OrderListRequest: {request}")
    tm = ToolManager()
    result = tm.get_order_list(request.phone_number)
    return result


@app.post('/groq_stt')
async def groq_stt(file: UploadFile = File(...)):
    """Speech-to-Text: Accepts an audio file, returns transcribed text using Groq API."""
    if Groq is None:
        return JSONResponse({"error": "groq package not installed"}, status_code=500)
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        return JSONResponse({"error": "GROQ_API_KEY not set in environment"}, status_code=500)
    client = Groq(api_key=api_key)
    try:
        contents = await file.read()
        # Groq expects a file-like object, so use BytesIO
        import io
        audio_file = io.BytesIO(contents)
        audio_file.name = file.filename or "audio.wav"
        transcription = client.audio.transcriptions.create(
            file=audio_file,
            model="whisper-large-v3-turbo",
            response_format="json",
            language="en"
        )
        return JSONResponse({"text": transcription.text})
    except Exception as e:
        logger.error(f"Groq STT error: {e}")
        return JSONResponse({"error": str(e)}, status_code=500)

@app.post('/groq_tts')
async def groq_tts(
    text: str = Form(...),
    voice: str = Form(os.getenv("PLAYTTS_VOICE")),
    model: str = Form("playai-tts"),
    response_format: str = Form("wav")
):
    """Text-to-Speech: Accepts text, returns audio using Groq API."""
    logger.info(f"Using Voice : {os.getenv("PLAYTTS_VOICE")}")
    if Groq is None:
        return JSONResponse({"error": "groq package not installed"}, status_code=500)
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        return JSONResponse({"error": "GROQ_API_KEY not set in environment"}, status_code=500)
    client = Groq(api_key=api_key)
    try:
        response = client.audio.speech.create(
            model=model,
            voice=voice,
            input=text,
            response_format=response_format
        )
        audio_bytes = response.read()  # Correct way to get bytes from BinaryAPIResponse
        return StreamingResponse(
            iter([audio_bytes]),
            media_type=f"audio/{response_format}",
            headers={
                "Content-Disposition": f"attachment; filename=speech.{response_format}"
            }
        )
    except Exception as e:
        logger.error(f"Groq TTS error: {e}")
        return JSONResponse({"error": str(e)}, status_code=500)


if __name__ == '__main__':
    import uvicorn
    import argparse
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi import FastAPI

    # Create FastAPI app instance
    app = FastAPI()

    # Allow all origins
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Parse command line arguments for port and host
    parser = argparse.ArgumentParser(description='Run the FastAPI server.')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='Host to run the server on')
    parser.add_argument('--port', type=int, default=8000, help='Port to run the server on')
    args = parser.parse_args()

    print(f"Running server on {args.host}:{args.port}")
    uvicorn.run(
        'src.core:app',
        host=args.host,
        port=args.port,
        reload=True,
        reload_dirs=[str(THIS_DIR.parent)]
    )
