import requests

API_URL = "http://localhost:8000/groq_tts"

def test_groq_tts():
    data = {
        "text": "Hello, this is a test of Groq text to speech.",
        "voice": "Briggs-PlayAI",
        "model": "playai-tts",
        "response_format": "wav"
    }
    response = requests.post(API_URL, data=data)
    breakpoint()
    print("Status Code:", response.status_code)
    print("Headers:", response.headers)
    # Optionally save the audio file for manual inspection
    if response.ok:
        breakpoint()
        with open("test_output.wav", "wb") as f:
            f.write(response.content)
        print("Audio file saved as test_output.wav")
    else:
        print("Error:", response.text)

if __name__ == "__main__":
    test_groq_tts()
