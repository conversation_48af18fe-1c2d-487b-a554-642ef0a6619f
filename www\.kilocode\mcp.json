{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Brave Search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAbycu8bqiH1-MUxlvCsXfXVC8P4-H"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "alwaysAllow": ["browser_navigate"]}, "Crawl4AI-Rag": {"command": "uvx", "args": ["mcp-proxy", "http://localhost:2121/sse"]}, "mem0-memory-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@mem0ai/mem0-memory-mcp", "--key", "39f11cc2-792d-4a7a-b208-9fabf29bd918", "--profile", "equal-condor-chb0F2"]}, "server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--key", "39f11cc2-792d-4a7a-b208-9fabf29bd918"]}, "mcp-server-sqlite-npx": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "mcp-server-sqlite-npx", "--key", "39f11cc2-792d-4a7a-b208-9fabf29bd918", "--profile", "equal-condor-chb0F2"]}, "gitingest-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@puravparab/gitingest-mcp", "--key", "39f11cc2-792d-4a7a-b208-9fabf29bd918"]}, "time-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@yokingma/time-mcp", "--key", "39f11cc2-792d-4a7a-b208-9fabf29bd918"]}, "shadcn": {"description": "", "type": "stdio", "command": "npx", "args": ["-y", "shadcn@canary", "registry:mcp"], "env": {"REGISTRY_URL": "https://alpine-registry.vercel.app//r/registry.json"}}}}