# Base image
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml uv.lock /app/

# Install dependencies
RUN pip install uv && \
    uv pip install --system --no-cache .

# Copy source code
COPY src /app/src/
COPY server.py /app/

# Expose port
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]