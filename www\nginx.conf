server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index chatapp.html;

    location / {
        try_files $uri $uri/ /chatapp.html;
    }

    # Optional: Add a location block if you want to proxy API requests
    # from the frontend to the backend through Nginx, to avoid CORS issues
    # if the backend isn't configured for it.
    # location /api/ {
    #    proxy_pass http://backend:8000/; # 'backend' is the service name in docker-compose
    #    proxy_set_header Host $host;
    #    proxy_set_header X-Real-IP $remote_addr;
    #    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #    proxy_set_header X-Forwarded-Proto $scheme;
    # }
}