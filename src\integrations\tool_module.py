from supabase import create_client
from pydantic_ai import Tool
from pydantic import BaseModel, Field

from google import genai
from google.genai.types import EmbedContentConfig

from loguru import logger

from dotenv import load_dotenv
import os
from typing import Tuple, Optional
import httpx
import json
load_dotenv()

# Logging setup as per project rules
LOG_DIR = os.path.join(os.path.dirname(__file__), '../../logs/tool_module')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

# Pydantic models for tool arguments
class GetVehicleQuickStatArgs(BaseModel):
    vin_number: str = Field(..., description="The VIN number of the vehicle.")

class GetOrderListArgs(BaseModel):
    phone_number: str = Field(..., description="The user's phone number.")

class GetRuleIdFromUserQueryArgs(BaseModel):
    user_query: str = Field(..., description="The customer's complaint or query in natural language.")
    match_count: int = Field(default=3, description="Number of top rule IDs to return. Defaults to 3.")

class RetrieveDocumentsArgs(BaseModel):
    query: str = Field(..., description="The user's question or query to search for in documents.")
    match_count: int = Field(default=5, description="Number of document matches to return. Defaults to 5.")

class RetrieveFeedbackArgs(BaseModel):
    query: str = Field(..., description="The user's question or query to search for in feedback.")
    match_count: int = Field(default=5, description="Number of feedback matches to return. Defaults to 5.")

class GetOrderDetailsArgs(BaseModel):
    phone_no: str = Field(..., description="The user's phone number.")
    order_id: str = Field(..., description="The order ID to fetch details for.")


class ToolManager:
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_DATA_API')
        self.supabase_key = os.getenv('SUPABASE_PSQL_SERVICE_KEY')
        self.supabase_client = create_client(self.supabase_url, self.supabase_key)
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_client = genai.Client(api_key=self.gemini_api_key)

    def check_health(self) -> Tuple[bool, Optional[str]]:
        try:
            logger.debug("Testing Supabase connection with a simple query")
            _ = self.supabase_client.from_('document_rows').select('row_data').limit(1).execute()
            logger.debug("Supabase connection test successful")
            return True, None
        except Exception as e:
            logger.error(f"Error checking health: {e}")
            return False, str(e)

    async def get_embeddings(self, text: str) -> list[float]:
        """Get text embeddings from Gemini API.

        Args:
            text (str): The input text to embed.

        Returns:
            list[float]: The embedding vector for the input text.

        Raises:
            ValueError: If the Gemini API key is not set.
            RuntimeError: If the embedding request fails.
        """
        
        if not self.gemini_api_key:
            logger.error("GEMINI_API_KEY must be set in environment variables")
            raise ValueError("GEMINI_API_KEY must be set in environment variables")

        try:
            gemini_client = genai.Client(api_key=self.gemini_api_key)
            result = gemini_client.models.embed_content(
                model="models/text-embedding-004",
                contents=[text],
                config=EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
            )
            embedding = result.embeddings[0].values
            logger.debug(f"Generated embedding for text: {text[:30]}... (len={len(embedding)})")
            return embedding
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            # Return a zero vector of length 768 as fallback
            return [0.0] * 768

    async def get_tools(self) -> list[Tool]:
        """
        Return a list of Tool objects for agent use.
        """
        tools = [
            Tool(
                function=self.get_vehicle_quick_stat,
                name="quick_stat",
                description="Fetch quick vehicle stats by VIN number. Output: dict with vehicle stats or error info.",
                args_schema=GetVehicleQuickStatArgs
            ),
            Tool(
                function=self.get_order_list,
                name="order_list",
                description="Fetch order list for a user by phone number. Output: dict with order data or error info.",
                args_schema=GetOrderListArgs
            ),
            Tool(
                function=self.get_rule_id_from_user_query,
                name="rule_id_from_user_query",
                description="Retrieve the most relevant rule ID(s) from the rule engine based on a user's complaint or query. Output: list of rule IDs (as strings) most relevant to the user query.",
                args_schema=GetRuleIdFromUserQueryArgs
            ),
            Tool(
                function=self.retrieve_documents,
                name="retrieve_documents",
                description="Retrieve relevant documentation from the documents_768 table via Supabase RPC. Output: Formatted string with documentation or error.",
                args_schema=RetrieveDocumentsArgs
            ),
            Tool(
                function=self.retrieve_feedback,
                name="retrieve_feedback",
                description="Retrieve relevant user feedback from the feedback_768 table via Supabase RPC. Output: Formatted string with feedback or error.",
                args_schema=RetrieveFeedbackArgs
            ),
            Tool(
                function=self.get_order_details,
                name="order_details",
                description="Fetch order details for a user by phone number and order ID. Output: dict with order details or error info.",
                args_schema=GetOrderDetailsArgs
            )
        ]
        return tools

    def create_embeddings_batch(self, texts: list[str]) -> list[list[float]]:
        """Create embeddings for multiple texts in a single API call using Gemini client.

        Args:
            texts (list[str]): List of texts to create embeddings for.

        Returns:
            list[list[float]]: List of embeddings (each embedding is a list of floats).
        """
        if not texts:
            return []
        try:
            result = self.gemini_client.models.embed_content(
                model="models/text-embedding-004",
                contents=texts,
                config=EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
            )
            embeddings = [embedding.values for embedding in result.embeddings]
            logger.debug(f"Generated batch embeddings for {len(texts)} texts.")
            return embeddings
        except Exception as e:
            logger.error(f"Error creating batch embeddings: {e}")
            return [[0.0] * 768 for _ in range(len(texts))]

    def create_embedding(self, text: str) -> list[float]:
        """Create an embedding for a single text using Gemini client.

        Args:
            text (str): Text to create an embedding for.

        Returns:
            list[float]: The embedding vector for the input text.
        """
        try:
            embeddings = self.create_embeddings_batch([text])
            return embeddings[0] if embeddings else [0.0] * 768
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            return [0.0] * 768

    def retrieve_documents(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant documentation from the documents_768 table via Supabase RPC.

        Args:
            query (str): The user's question or query.
            match_count (int): Number of matches to return (default: 5).

        Returns:
            str: Formatted string containing the most relevant documentation chunks, or an error message.
        """
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return "Error: Supabase connection not available."
        try:
            logger.debug(f"Retrieving documents for query: '{query[:50]}...'")
            query_embedding = self.create_embedding(query)
            docs_result = self.supabase_client.rpc(
                "match_documents_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            if not docs_result.data:
                logger.info("No relevant documentation found for query")
                return "No relevant documentation found for your query."
            formatted_chunks = []
            for doc in docs_result.data:
                similarity_value = doc.get('similarity', 0)
                if isinstance(similarity_value, str):
                    similarity_pct = similarity_value
                else:
                    similarity_pct = f"{float(similarity_value) * 100:.2f}%"
                chunk_text = f"""
                # {doc.get('title', 'Document')} (Relevance: {similarity_pct})

                {doc.get('content', '')}

                Source: documents | ID: {doc.get('id', '')}
                """
                formatted_chunks.append(chunk_text.strip())
            return "\n\n---\n\n".join(formatted_chunks)
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}", exc_info=True)
            return f"Error retrieving documents: {e}"

    def retrieve_feedback(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant user feedback from the feedback_768 table via Supabase RPC.

        Args:
            query (str): The user's question or query.
            match_count (int): Number of matches to return (default: 5).

        Returns:
            str: Formatted string containing the most relevant feedback entries, or an error message.
        """
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return "Error: Supabase connection not available."
        try:
            logger.debug(f"Retrieving feedback for query: '{query[:50]}...'")
            query_embedding = self.create_embedding(query)
            feedback_result = self.supabase_client.rpc(
                "match_feedback_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            if not feedback_result.data:
                logger.info("No relevant user feedback found for query")
                return "No relevant user feedback found for your query."
            formatted_chunks = []
            for item in feedback_result.data:
                similarity_value = item.get('similarity', 0)
                if isinstance(similarity_value, str):
                    similarity_pct = similarity_value
                else:
                    similarity_pct = f"{float(similarity_value) * 100:.2f}%"
                is_helpful = item.get("is_helpful", True)
                helpful_indicator = "👍 Helpful" if is_helpful else "👎 Not Helpful"
                chunk_text = f"""
                # {item.get('title', 'User Feedback')} (Relevance: {similarity_pct})

                {item.get('content', '')}

                {helpful_indicator} | Source: feedback | ID: {item.get('id', '')}
                """
                formatted_chunks.append(chunk_text.strip())
            return "\n\n---\n\n".join(formatted_chunks)
        except Exception as e:
            logger.error(f"Error retrieving feedback: {e}", exc_info=True)
            return f"Error retrieving feedback: {e}"

    def get_rule_id_from_user_query(self, user_query: str, match_count: int = 3) -> list[str]:
        """
        Retrieve the most relevant rule ID(s) from the rule engine based on a user's complaint or query.

        This tool is designed for the AI Agent to map a customer's complaint or natural language query to the corresponding rule(s) in the rule engine. It uses vector similarity search (via the `match_rule_engine_768` Supabase RPC on the `rule_engine_768` table) to find the best-matching rule(s) for the input query. The returned rule ID(s) can then be used to fetch the full rule or rule flow from the MCP or Supabase.

        Args:
            user_query (str): The customer's complaint or query in natural language.
            match_count (int, optional): Number of top rule IDs to return. Defaults to 3 (best matches).

        Returns:
            list[str]: List of rule IDs (as strings) most relevant to the user query. Returns an empty list if no match is found or on error.

        Example:
            >>> tm = ToolManager()
            >>> rule_ids = tm.get_rule_id_from_user_query('My scooter won\'t start')
            >>> print(rule_ids)
            ['001']
        """
        import re
        logger.info(f"[get_rule_id_from_user_query] Called with user_query={user_query!r}, match_count={match_count!r}")
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return []
        try:
            logger.debug(f"[get_rule_id_from_user_query] Creating embedding for user_query: {user_query!r}")
            query_embedding = self.create_embedding(user_query)
            logger.debug(f"[get_rule_id_from_user_query] Embedding: {query_embedding[:10]}... (len={len(query_embedding)})")
            logger.debug(f"[get_rule_id_from_user_query] Calling Supabase RPC 'match_rule_mapping_768' with embedding and match_count={match_count}")
            rules_result = self.supabase_client.rpc(
                "match_rule_mapping_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            logger.debug(f"[get_rule_id_from_user_query] Supabase RPC response: {rules_result}")
            if not rules_result.data:
                logger.info("[get_rule_id_from_user_query] No relevant rule found for user query")
                return []
            rule_ids = []
            for row in rules_result.data:
                content = row.get('content', '')
                logger.debug(f"[get_rule_id_from_user_query] Row content: {content}")
                # Find all rule IDs after TRUE : or FALSE : (e.g., TRUE : 026, FALSE : 004)
                matches = re.findall(r'\b(?:TRUE|FALSE)\s*:\s*(\d+)\b', content)
                logger.debug(f"[get_rule_id_from_user_query] Extracted matches from content: {matches}")
                rule_ids.extend(matches)
            logger.info(f"[get_rule_id_from_user_query] Extracted rule IDs: {rule_ids}")
            return rule_ids
        except Exception as e:
            logger.error(f"[get_rule_id_from_user_query] Error retrieving rule ID(s): {e}", exc_info=True)
            return []

    def get_vehicle_quick_stat(self, vin_number: str) -> dict:
        """
        Fetch vehicle quick stats from Voltron API and return vital fields only.
        Args:
            vin_number (str): The VIN number of the vehicle.
        Returns:
            dict: Slim dict with vital vehicle stats or error info.
        """
        voltron_url = f"https://voltron-8080a.corp.olaelectric.com/api/v1/vehicle/{vin_number}"
        cookie = os.getenv("VOLTRON_API_COOKIE")
        headers = {"cookie": cookie, "Cookie":cookie} if cookie else {}
        try:
            with httpx.Client(timeout=10) as client:
                resp = client.get(voltron_url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                # Accept both list and dict responses
                if isinstance(data, dict):
                    d = data.get("data", {})
                elif isinstance(data, list) and data:
                    d = data[0].get("data", {})
                else:
                    return {"error": "Unexpected response format from Voltron API"}
                # Extract vital fields only
                return {
                    "vehicleId": d.get("vehicleId"),
                    "totalDistanceCovered": d.get("totalDistanceCovered", {}).get("value"),
                    "vehicleStatus": d.get("vehicleStatus", {}).get("value"),
                    "chargingStatus": d.get("chargingStatus", {}).get("value"),
                    "location": {
                        "latitude": d.get("location", {}).get("latitude"),
                        "longitude": d.get("location", {}).get("longitude"),
                    },
                    "range": {
                        "ai": d.get("range", {}).get("rangeAi"),
                        "eco": d.get("range", {}).get("rangeEcoAi"),
                        "sport": d.get("range", {}).get("rangeSportAi"),
                        "hyper": d.get("range", {}).get("rangeHyperAi"),
                        "custom": d.get("range", {}).get("rangeCustomAi"),
                    },
                    "batteryCharge": d.get("averageChargeInfo", {}).get("batteryCharge"),
                    "driveMode": d.get("driveMode", {}).get("value"),
                    "lockStatus": d.get("lockStatus", {}).get("value"),
                    "sleepState": d.get("sleepState", {}).get("value"),
                }
        except Exception as e:
            logger.error(f"Error fetching quick stat for {vin_number}: {e}")
            return {"error": str(e)}

    def get_order_list(self, phone_number: str) -> dict:
        """
        Fetch order list for a user by phone number using two-step API calls.
        1. POST to user details endpoint to get tenant UUID.
        2. GET to order list endpoint with tenant UUID to get orders.
        Returns dict with order data or error info.
        """
        import httpx
        logger.info(f"[get_order_list] Called with phone_number={phone_number!r}")
        # Step 1: Get tenant UUID
        user_details_url = "https://fury-arc-8080a.corp.olaelectric.com/v1/internal/user/2w/details/phone"
        user_details_headers = {
            "client": "arcee",
            "client_token": "64148c21-b539-4a22-bbd7-c9577be16ce4",
            "Content-Type": "application/json"
        }
        try:
            with httpx.Client(timeout=10) as client:
                resp = client.get(user_details_url, params={"phone_number": phone_number}, headers=user_details_headers)
                try:
                    resp.raise_for_status()
                except httpx.HTTPStatusError as e:
                    if resp.status_code == 400:
                        logger.error(f"[get_order_list] Invalid or non-existent phone number: {phone_number!r}")
                        return {"error": "Invalid or non-existent phone number", "details": resp.text}
                    else:
                        logger.error(f"[get_order_list] Error fetching tenant_uuid: {e}")
                        return {"error": f"Error fetching tenant_uuid: {e}", "details": resp.text}
                user_data = resp.json()
                tenant_uuid = user_data.get("data", {}).get("user_tenant_uuid")
                if not tenant_uuid:
                    logger.error(f"[get_order_list] No tenant_uuid found for phone_number={phone_number!r}")
                    return {"error": "No tenant_uuid found for this phone number", "details": user_data}
        except Exception as e:
            logger.error(f"[get_order_list] Error fetching tenant_uuid: {e}")
            return {"error": f"Error fetching tenant_uuid: {e}"}
        # Step 2: Get order list
        order_list_url = f"https://arcee-8080a.ev.corp.olaelectric.com/v1/order/{tenant_uuid}"
        order_list_headers = {"accept": "*/*"}
        order_list_params = {"initial_prebooking": "false", "populateInternalScooterModel": "false"}
        try:
            with httpx.Client(timeout=10) as client:
                resp = client.get(order_list_url, headers=order_list_headers, params=order_list_params)
                resp.raise_for_status()
                order_data = resp.json()
                if not order_data.get("user_orders"):
                    logger.warning(f"[get_order_list] No orders found for tenant_uuid={tenant_uuid}")
                    return {"info": "No orders found for this user", "details": order_data}
                return order_data
        except Exception as e:
            logger.error(f"[get_order_list] Error fetching order list: {e}")
            return {"error": f"Error fetching order list: {e}"}

    async def get_order_details(self, phone_no: str, order_id: str) -> dict:
        """
        Fetch order details for a user by phone number and order ID using two-step API calls.
        1. GET to user details endpoint to get user_tenant_uuid.
        2. GET to order details endpoint with user_tenant_uuid and order_id to get order details.
        Returns dict with order data or error info.
        """
        logger.info(f"[get_order_details] Called with phone_no={phone_no!r}, order_id={order_id!r}")

        # Step 1: Get User Tenant UUID
        user_details_url = "http://fury-arc-8080a.corp.olaelectric.com/v1/internal/user/2w/details/phone"
        user_details_headers = {
            "client": "arcee",
            "client_token": "64148c21-b539-4a22-bbd7-c9577be16ce4",
            "Cookie": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw", # As per plan
            "cookie": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw;" # As per plan
        }
        user_details_params = {"phone_number": phone_no}
        user_tenant_uuid = None

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                logger.debug(f"[get_order_details] Step 1: Requesting user tenant UUID from {user_details_url} with params {user_details_params}")
                resp = await client.get(user_details_url, params=user_details_params, headers=user_details_headers)
                logger.debug(f"[get_order_details] Step 1: Response status: {resp.status_code}, Response text: {resp.text[:200]}")
                
                if resp.status_code == 400:
                    logger.error(f"[get_order_details] Invalid or non-existent phone number: {phone_no!r}. Response: {resp.text}")
                    return {"error": "Invalid or non-existent phone number", "details": resp.text}
                
                resp.raise_for_status() # Raise an exception for 4xx/5xx errors not caught above
                
                user_data = resp.json()
                user_tenant_uuid = user_data.get("data", {}).get("user_tenant_uuid")

                if not user_tenant_uuid:
                    logger.error(f"[get_order_details] user_tenant_uuid not found in response for phone_no={phone_no!r}. Response data: {user_data}")
                    return {"error": "user_tenant_uuid not found in response", "details": user_data}
                logger.info(f"[get_order_details] Successfully fetched user_tenant_uuid: {user_tenant_uuid} for phone_no={phone_no!r}")

        except httpx.HTTPStatusError as e:
            logger.error(f"[get_order_details] HTTPStatusError while fetching user_tenant_uuid for phone_no={phone_no!r}: {e}. Response: {e.response.text}")
            return {"error": f"HTTP error fetching user tenant UUID: {e.response.status_code}", "details": e.response.text}
        except httpx.RequestError as e:
            logger.error(f"[get_order_details] RequestError while fetching user_tenant_uuid for phone_no={phone_no!r}: {e}")
            return {"error": f"Request error fetching user tenant UUID: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"[get_order_details] JSONDecodeError while parsing user_tenant_uuid response for phone_no={phone_no!r}: {e}. Response text: {resp.text}")
            return {"error": "Failed to parse user tenant UUID response as JSON", "details": resp.text}
        except Exception as e:
            logger.error(f"[get_order_details] Unexpected error while fetching user_tenant_uuid for phone_no={phone_no!r}: {e}", exc_info=True)
            return {"error": f"Unexpected error fetching user tenant UUID: {str(e)}"}

        # Step 2: Get Order Details
        order_details_url = f"https://arcee-8080b.corp.olaelectric.com/v1/order/{order_id}/details"
        order_details_headers = {
            "accept": "*/*",
            "X-Purchase-Flow": "purchase_flow",
            "locale": "en",
            "x-utm-uuid": user_tenant_uuid
        }

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                logger.debug(f"[get_order_details] Step 2: Requesting order details from {order_details_url} with x-utm-uuid: {user_tenant_uuid}")
                resp = await client.get(order_details_url, headers=order_details_headers)
                logger.debug(f"[get_order_details] Step 2: Response status: {resp.status_code}, Response text: {resp.text[:200]}")
                
                resp.raise_for_status() # Raise an exception for 4xx/5xx errors
                
                order_data = resp.json()
                logger.info(f"[get_order_details] Successfully fetched order details for order_id={order_id!r}")
                return order_data

        except httpx.HTTPStatusError as e:
            logger.error(f"[get_order_details] HTTPStatusError while fetching order details for order_id={order_id!r}: {e}. Response: {e.response.text}")
            return {"error": f"HTTP error fetching order details: {e.response.status_code}", "details": e.response.text}
        except httpx.RequestError as e:
            logger.error(f"[get_order_details] RequestError while fetching order details for order_id={order_id!r}: {e}")
            return {"error": f"Request error fetching order details: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"[get_order_details] JSONDecodeError while parsing order details response for order_id={order_id!r}: {e}. Response text: {resp.text}")
            return {"error": "Failed to parse order details response as JSON", "details": resp.text}
        except Exception as e:
            logger.error(f"[get_order_details] Unexpected error while fetching order details for order_id={order_id!r}: {e}", exc_info=True)
            return {"error": f"Unexpected error fetching order details: {str(e)}"}

if __name__ == "__main__":
    # Simple tests for ToolManager embedding and retrieval methods
    tm = ToolManager()

    print("\nTesting get_order_list:")
    try:
        phone_number = "9826000000"
        order_list = tm.get_order_list(phone_number)
        print(f"Order list for phone number '{phone_number}': {order_list}")
    except Exception as e:
        print(f"Error in get_order_list: {e}")
