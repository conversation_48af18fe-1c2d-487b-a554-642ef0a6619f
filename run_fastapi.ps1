# PYTHONPATH
$env:PYTHONPATH = "."
# Load all env vars from .env
Get-Content .env | ForEach-Object {
    $name, $value = $_.split('=')
    if ([string]::IsNullOrWhiteSpace($name) -eq $false) {
        ${env:$name} = $value
    }
}
Write-Host "Attempting to start FastAPI application on http://0.0.0.0:8000"
Write-Host "If successful, the server will be running. Press CTRL+C to stop."
uv run src/core.py --host 0.0.0.0 --port 8000