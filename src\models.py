from typing import Literal
from typing_extensions import TypedDict
from pydantic import BaseModel, Field

class ChatMessage(TypedDict):
    """Format of messages sent to the browser."""

    role: Literal['user', 'model']
    timestamp: str
    content: str

class RequestModel(BaseModel):
    user_id: str
    session_id: str
    prompt: str
    model: str
    temperature: float = 0.2
    enable_memory: bool = True

class QuickStatRequest(BaseModel):
    vin_number: str

class OrderListRequest(BaseModel):
    phone_number: str = Field(..., pattern=r"^\d{10}$")
class OrderDetailsRequest(BaseModel):
    phone_no: str
    order_id: str