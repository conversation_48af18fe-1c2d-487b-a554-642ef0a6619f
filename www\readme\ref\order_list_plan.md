### `/order_list` endpoint
Implement vehicle quick stat endpoint to get this data into context.
Brief PLAN is to implement the this within @tool_module.py so that this can be used as both:
- As a tool to the agent
- As a direct endpoint available @core.py fastapi

### N8N Node Details
```tenant_id json
{
  "nodes": [
    {
      "parameters": {
        "url": "http://fury-arc-8080a.corp.olaelectric.com/v1/internal/user/2w/details/phone",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "phone_number",
              "value": "={{ $json.body?.phone_number }}"
            }
          ]
        },
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "client",
              "value": "arcee"
            },
            {
              "name": "client_token",
              "value": "64148c21-b539-4a22-bbd7-c9577be16ce4"
            }
          ]
        },
        "options": {
          "redirect": {
            "redirect": {}
          }
        }
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        540,
        -60
      ],
      "id": "a2d1a150-9b8a-4cd6-9338-5096dca77d84",
      "name": "HTTP Request",
      "executeOnce": false,
      "onError": "continueErrorOutput"
    }
  ],
  "connections": {
    "HTTP Request": {
      "main": [
        []
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "87f543696fbbc9747c32cfec9522418972a08ad56edb74442a02423716dbf09d"
  }
}
```

```order_id_list json
{
  "nodes": [
    {
      "parameters": {
        "url": "=https://arcee-8080a.ev.corp.olaelectric.com/v1/order/{{ $json.data.user_tenant_uuid }}",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "initial_prebooking",
              "value": "false"
            },
            {
              "name": "populateInternalScooterModel",
              "value": "false"
            }
          ]
        },
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "accept",
              "value": "*/*"
            }
          ]
        },
        "options": {
          "redirect": {
            "redirect": {}
          }
        }
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        740,
        -280
      ],
      "id": "4cbb202c-efcc-461a-8816-7222ac9c9785",
      "name": "Order Id List"
    }
  ],
  "connections": {
    "Order Id List": {
      "main": [
        []
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "87f543696fbbc9747c32cfec9522418972a08ad56edb74442a02423716dbf09d"
  }
}
```

### Inputs
Handle edge cases for invalid phone numbers.
```json
{
  "phone_number": "9826000000"
}
```

### Response Received
```json
{
  "user_id": "e030b6e3-b5ca-49cb-a2d6-618ec4c9323c",
  "user_orders": [
    {
      "is_international_order": false,
      "order_id": "OET-872473106010325-XVK497",
      "user_id": "e030b6e3-b5ca-49cb-a2d6-618ec4c9323c",
      "status": "DELIVERED",
      "order_metadata": {
        "email_image_url": null,
        "color": "Midnight Blue",
        "image_url": "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0000.webp",
        "name": "Ola S1 X 3rd Gen 3kWh",
        "range": "181 KM",
        "ip_address": "**********",
        "image_carousal_list": [
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0000.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0001.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0002.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0003.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0004.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0005.webp"
        ],
        "speed": "115 KM/H",
        "helmet_details": {
          "helmetSize": "",
          "productId": "",
          "helmetPrice": 0
        },
        "helmet_opted_out": true,
        "insurance_code": "t10oMU5dY3nkB29lpwG%2B0asF%2BnWZpFYlp6rsTfJnAyI%3D",
        "fame_opted": true,
        "summary_invoked": true,
        "finance_opted": true,
        "journey_id": "33AD1F50E84949163AEBEDDA602B99AE5488485E0F555B0D08D1BADFEAB2646F",
        "invoiceToOfsStatusMap": {
          "down_payment_receipt": {
            "last_try_at": "2025-03-27T08:32:41.655",
            "invoiceId": 12200420,
            "error": "",
            "status": "SUCCESS"
          },
          "advance_payment_receipt": {
            "last_try_at": "2025-03-27T08:32:42.175",
            "invoiceId": 12200421,
            "error": "",
            "status": "SUCCESS"
          },
          "reservation_payment_receipt": {
            "last_try_at": "2025-03-27T08:32:42.137",
            "invoiceId": 12200422,
            "error": "",
            "status": "SUCCESS"
          },
          "fame_receipt": {
            "last_try_at": "2025-04-14T13:59:19.832",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "split_payment_receipt": {
            "last_try_at": "2025-04-14T13:59:19.840",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "accessory_payment_receipt": {
            "last_try_at": "2025-04-14T13:59:19.845",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "vehicle_invoice": {
            "last_try_at": "2025-04-14T13:59:19.794",
            "invoiceId": 12703856,
            "error": "",
            "status": "SUCCESS"
          },
          "charger_invoice": {
            "last_try_at": "2025-04-14T13:59:19.800",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "registration_invoice": {
            "last_try_at": "2025-03-27T08:32:41.248",
            "invoiceId": 12435420,
            "error": "",
            "status": "SUCCESS"
          },
          "EXTENDED_WARRANTY_INVOICE": {
            "last_try_at": "2025-04-14T13:59:19.808",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "EXTENDED_WARRANTY_INVOICE_OEM": {
            "last_try_at": "2025-04-14T13:59:19.813",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "performance_upgrade_invoice": {
            "last_try_at": "2025-04-25T20:02:17.815",
            "invoiceId": 12707224,
            "error": "",
            "status": "SUCCESS"
          },
          "rto_acknowledgement": {
            "last_try_at": "2025-04-25T20:02:39.011",
            "invoiceId": null,
            "error": "Invoice not found",
            "status": "FAILURE"
          },
          "vehicle_insurance_invoice": {
            "last_try_at": "2025-04-14T13:59:40.247",
            "invoiceId": 0,
            "error": "",
            "status": "SUCCESS"
          }
        },
        "fame_approved": true,
        "fame_approved_date": "03/03/2025",
        "no_dues": true,
        "sdc": "PNBE",
        "invoice_generated": true,
        "loan_disbursed": true,
        "loan_disbursed_timestamp": 1744619226993,
        "invoiceStatus": "CANCELED",
        "insurance_policy_rollout": true,
        "insurance_policy_rollout_time": "2025-04-14 13:59:18.79",
        "insurance_policy_details": {
          "insurerName": "ICICI LOMBARD",
          "policyNo": "3005/388838333/00/000",
          "policyDownloadUrl": "https://s3-ap-south-1.amazonaws.com/prod-om/ofs-files-processing/credit-card/insurance/data/policies/vehicle/EV-ICICI-e030b6e3-b5ca-49cb-a2d6-618ec4c9323c-OET-872473106010325-XVK497.PDF?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250414T082937Z&X-Amz-SignedHeaders=host&X-Amz-Expires=431999&X-Amz-Credential=AKIA3MMD3GQGSOIQXXIW%2F20250414%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Signature=30535463dc32b1885796a7d1df673c2ed56b7a6a1e66bb11e1dc03e56bb3d1a1",
          "policyStartDate": "14/04/2025",
          "policyEndDate": "13/04/2030",
          "totalPremium": "6901.0",
          "providedBy": "OFS",
          "sumInsured": "104499.0"
        },
        "c_app_mapping_status": "SUCCESS",
        "uda_id": "dde49a30-30b0-4290-80a0-b7eaf0826d57",
        "invoice_status": "Sent to LMD",
        "fancy_opted": false,
        "BH_FANCY_RECEIPT": false
      },
      "created": "2025-03-01T01:01:47.000+00:00",
      "purchase_whitelisted": true,
      "balance_payment_whitelisted": true,
      "status_color": null,
      "special_order": null,
      "auto_whitelisted": true,
      "referral_code": null,
      "consent": {
        "privacy_consent": true
      },
      "order_source": null,
      "order_source_id": null,
      "order_id_suffix": "XVK497"
    },
    {
      "is_international_order": false,
      "order_id": "OET-425061710190225-OOS412",
      "user_id": "e030b6e3-b5ca-49cb-a2d6-618ec4c9323c",
      "status": "REFUND_INITIATED",
      "order_metadata": {
        "email_image_url": null,
        "color": "Midnight Blue",
        "image_url": "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0000.webp",
        "name": "Ola S1 X 3rd Gen 3kWh",
        "range": "181 KM",
        "ip_address": "**********",
        "image_carousal_list": [
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0000.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0001.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0002.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0003.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0004.webp",
          "https://assets.olaelectric.com/olaelectric-videos/configs-static/assets/ola_s1_x_3rdgen/midnight_blue/Buying_Floor_S1x_S1X_Mblue0005.webp"
        ],
        "speed": "115 KM/H",
        "helmet_details": {
          "helmetSize": "",
          "productId": "",
          "helmetPrice": 0
        },
        "helmet_opted_out": true,
        "fame_opted": true,
        "summary_invoked": true,
        "finance_opted": false,
        "insurance_code": "du%2BsBTnS%2Bmc7XAOifDuXePFHc9uUynddDNE1AOgbUuo%3D",
        "user_requested_cancel": true,
        "cancel_requested": true,
        "eCommerceInboundCancel": false,
        "cancellationReason": {
          "L1": "Loan-related Issues",
          "L2": "Other - Actually I have selected the wrong method and I have reordered with the different order id"
        },
        "refundable_amount": "109903.0",
        "fancy_opted": false,
        "BH_FANCY_RECEIPT": false
      },
      "created": "2025-02-19T04:47:06.000+00:00",
      "purchase_whitelisted": true,
      "balance_payment_whitelisted": true,
      "status_color": "#D19126",
      "special_order": null,
      "auto_whitelisted": true,
      "referral_code": null,
      "consent": {
        "privacy_consent": true
      },
      "order_source": null,
      "order_source_id": null,
      "order_id_suffix": "OOS412"
    }
  ],
  "running_campaign": null
}
```